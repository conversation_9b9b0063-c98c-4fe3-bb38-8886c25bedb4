import os
import json
import time
import pyperclip
import requests
import gspread
import pyautogui
import re
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from oauth2client.service_account import ServiceAccountCredentials
from webdriver_manager.chrome import ChromeDriverManager
import sys
import random
import math
import platform
from googleapiclient.discovery import build
sys.stdout.reconfigure(encoding='utf-8')

# ───── SETUP ─────────────────────────────────────────────────────────────────────

def get_driver():
    chrome_options = webdriver.ChromeOptions()
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--disable-logging")
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option("useAutomationExtension", False)

    try:
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
    except Exception as e:
        print(f"[WARNING] ChromeDriverManager failed: {e}")
        try:
            driver = webdriver.Chrome(options=chrome_options)
        except Exception as e2:
            print(f"[ERROR] Both methods failed: {e2}")
            raise

    return driver

def get_google_sheet_data():
    scope = ["https://spreadsheets.google.com/feeds", "https://www.googleapis.com/auth/drive"]
    creds_path = r"./credentials.json"
    creds = ServiceAccountCredentials.from_json_keyfile_name(creds_path, scope)
    client = gspread.authorize(creds)
    sheet = client.open_by_key("1F0QwNeAi35W9ZCRgywswGRrM11_HumETGak6zRHXizU").worksheet("Sheet1")
    return sheet.get_all_records()

def get_drive_service():
    """Tạo service để truy cập Google Drive API với quyền đọc và xóa"""
    scope = [
        "https://www.googleapis.com/auth/drive.readonly",
        "https://www.googleapis.com/auth/drive.file"  # Thêm quyền xóa file
    ]
    creds_path = r"./credentials.json"
    creds = ServiceAccountCredentials.from_json_keyfile_name(creds_path, scope)
    service = build('drive', 'v3', credentials=creds)
    return service

def get_folder_id_from_url(folder_url):
    """Lấy folder ID từ URL Google Drive folder"""
    print(f"[DEBUG] Processing folder URL: {folder_url}")
    match = re.search(r"folders/([a-zA-Z0-9_-]+)", folder_url)
    if match:
        folder_id = match.group(1)
        print(f"[DEBUG] Extracted folder ID: {folder_id}")
        return folder_id
    print(f"[ERROR] Cannot extract folder ID from URL")
    return None

def test_folder_access(folder_url):
    """Test function để kiểm tra quyền truy cập folder - tối ưu tốc độ"""
    folder_id = get_folder_id_from_url(folder_url)
    if not folder_id:
        return False

    try:
        service = get_drive_service()
        results = service.files().list(
            q=f"'{folder_id}' in parents and trashed=false",
            fields="files(id, name)",
            pageSize=1
        ).execute()
        return len(results.get('files', [])) >= 0
    except:
        return False

def get_images_from_folder(folder_url):
    """Lấy danh sách tất cả file ảnh từ Google Drive folder với thông tin chi tiết"""
    folder_id = get_folder_id_from_url(folder_url)
    if not folder_id:
        print(f"[ERROR] Không thể lấy folder ID từ URL: {folder_url}")
        return []

    try:
        service = get_drive_service()
        print(f"[INFO] Connecting to Google Drive API...")

        # Check if folder is accessible
        try:
            folder_info = service.files().get(fileId=folder_id, fields="id,name").execute()
            print(f"[INFO] Accessing folder: {folder_info.get('name')}")
        except Exception as perm_error:
            print(f"[ERROR] Cannot access folder. Please check permissions: {perm_error}")
            return []

        # Lấy danh sách file trong folder
        query = f"'{folder_id}' in parents and trashed=false"
        print(f"[DEBUG] Query: {query}")

        all_files = []
        page_token = None

        while True:
            results = service.files().list(
                q=query,
                fields="nextPageToken, files(id, name, mimeType)",
                pageSize=1000,
                pageToken=page_token
            ).execute()

            files = results.get('files', [])
            all_files.extend(files)

            page_token = results.get('nextPageToken')
            if not page_token:
                break

        print(f"[INFO] Found {len(all_files)} total files in folder")

        # Trả về thông tin chi tiết của file ảnh (bao gồm cả file_id để xóa sau)
        image_files = []

        for file in all_files:
            print(f"[DEBUG] File: {file['name']} - Type: {file['mimeType']}")
            # Chỉ lấy file ảnh
            if file['mimeType'].startswith('image/'):
                image_info = {
                    'id': file['id'],
                    'name': file['name'],
                    'url': f"https://drive.google.com/file/d/{file['id']}/view",
                    'mimeType': file['mimeType']
                }
                image_files.append(image_info)
                print(f"[INFO] Tìm thấy ảnh: {file['name']}")

        print(f"[INFO] Tổng cộng {len(image_files)} ảnh trong folder")
        return image_files

    except Exception as e:
        print(f"[ERROR] Lỗi khi truy cập folder: {e}")
        import traceback
        traceback.print_exc()
        return []

def delete_files_from_drive(file_ids):
    """Xóa các file từ Google Drive"""
    if not file_ids:
        print("[INFO] Không có file nào để xóa")
        return True

    try:
        service = get_drive_service()
        print(f"[INFO] Đang xóa {len(file_ids)} file từ Google Drive...")

        deleted_count = 0
        for file_id in file_ids:
            try:
                # Xóa file (move to trash)
                service.files().delete(fileId=file_id).execute()
                print(f"[SUCCESS] Đã xóa file ID: {file_id}")
                deleted_count += 1
            except Exception as e:
                print(f"[ERROR] Không thể xóa file ID {file_id}: {e}")

        print(f"[INFO] Đã xóa thành công {deleted_count}/{len(file_ids)} file")
        return deleted_count == len(file_ids)

    except Exception as e:
        print(f"[ERROR] Lỗi khi xóa file: {e}")
        return False

def cleanup_downloaded_images(image_folder="image"):
    """Dọn dẹp folder ảnh đã download sau khi đăng bài"""
    try:
        if not os.path.exists(image_folder):
            print(f"[INFO] Folder {image_folder} không tồn tại")
            return True

        files = os.listdir(image_folder)
        if not files:
            print(f"[INFO] Folder {image_folder} đã trống")
            return True

        print(f"[INFO] Đang dọn dẹp {len(files)} file trong folder {image_folder}...")

        deleted_count = 0
        for file in files:
            file_path = os.path.join(image_folder, file)
            try:
                if os.path.isfile(file_path):
                    os.remove(file_path)
                    deleted_count += 1
                    print(f"[INFO] Đã xóa: {file}")
            except Exception as e:
                print(f"[ERROR] Không thể xóa {file}: {e}")

        print(f"[SUCCESS] Đã dọn dẹp {deleted_count}/{len(files)} file")
        return deleted_count == len(files)

    except Exception as e:
        print(f"[ERROR] Lỗi khi dọn dẹp folder: {e}")
        return False

def get_drive_direct_link(url):
    # Handle sharing links like: https://drive.google.com/file/d/ID/view?usp=sharing
    match = re.search(r"https://drive\.google\.com/file/d/([a-zA-Z0-9_-]+)", url)
    if match:
        file_id = match.group(1)
        return f"https://drive.google.com/uc?export=download&id={file_id}"

    # Handle direct links that already have the correct format
    if "drive.google.com/uc" in url and "export=download" in url:
        return url

    return url  # Return original URL if not a Google Drive link

def process_image_urls(image_input):
    """Xử lý input có thể là folder URL hoặc danh sách URL ảnh"""
    image_input = image_input.strip()
    print(f"[DEBUG] Processing image input: {image_input}")

    # Kiểm tra nếu là folder URL
    if "drive.google.com/drive/folders/" in image_input:
        print("[INFO] Phát hiện Google Drive folder, đang lấy danh sách ảnh...")

        # Test folder access first
        if not test_folder_access(image_input):
            print("[ERROR] Cannot access folder. Check if folder is public or service account has permission.")
            return []

        # Lấy thông tin chi tiết của ảnh (bao gồm file ID để xóa sau)
        image_files = get_images_from_folder(image_input)

        # Trả về format mới với thông tin chi tiết
        return {
            'type': 'folder',
            'folder_url': image_input,
            'images': image_files
        }

    # Nếu không phải folder, xử lý như danh sách URL thông thường
    urls = [url.strip() for url in image_input.split(",") if url.strip()]
    print(f"[INFO] Processing {len(urls)} individual image URLs")

    # Format cho individual URLs
    return {
        'type': 'individual',
        'images': [{'url': url} for url in urls]
    }

def download_images_from_data(image_data, save_folder="image"):
    """Download ảnh từ image_data (format mới)"""
    save_folder = os.path.normpath(save_folder)
    if not os.path.exists(save_folder):
        os.makedirs(save_folder)
        print(f"[INFO] Tạo folder: {save_folder}")

    if not image_data or not image_data.get('images'):
        print("[ERROR] Không có dữ liệu ảnh để download")
        return [], []

    images = image_data['images']
    image_paths = []
    file_ids = []  # Lưu file IDs để xóa sau

    print(f"[INFO] Bắt đầu download {len(images)} ảnh...")

    for i, img_info in enumerate(images, 1):
        # Lấy URL từ img_info
        if 'url' in img_info:
            url = img_info['url']
        else:
            print(f"[ERROR] Không tìm thấy URL cho ảnh {i}")
            continue

        url = url.strip()
        if not url:
            continue

        print(f"[INFO] Đang download ảnh {i}/{len(images)}: {url}")
        direct_url = get_drive_direct_link(url)
        print(f"[DEBUG] Direct URL: {direct_url}")

        # Generate safe filename
        if 'name' in img_info:
            # Sử dụng tên gốc từ Google Drive nhưng làm sạch ký tự đặc biệt
            original_name = img_info['name']
            # Loại bỏ ký tự đặc biệt và thay thế bằng underscore
            safe_name = re.sub(r'[<>:"/\\|?*]', '_', original_name)
            safe_name = re.sub(r'[^\w\-_\.]', '_', safe_name)
            name_without_ext = os.path.splitext(safe_name)[0]
            ext = os.path.splitext(safe_name)[1] or '.jpg'
            image_name = f"{name_without_ext}_{int(time.time())}{ext}"
        else:
            # Generate tên từ file ID
            if "drive.google.com" in url:
                file_id_match = re.search(r"[/=]([a-zA-Z0-9_-]{28,})", url)
                if file_id_match:
                    image_name = f"image_{file_id_match.group(1)[:10]}.jpg"
                else:
                    image_name = f"image_{int(time.time())}_{i}.jpg"
            else:
                image_name = os.path.basename(direct_url).split("?")[0]
                # Làm sạch tên file
                image_name = re.sub(r'[<>:"/\\|?*]', '_', image_name)
                image_name = re.sub(r'[^\w\-_\.]', '_', image_name)
                if not image_name.lower().endswith((".jpg", ".jpeg", ".png", ".gif", ".webp")):
                    image_name += ".jpg"

        image_path = os.path.join(save_folder, image_name)
        print(f"[DEBUG] Saving to: {image_path}")

        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }

            print(f"[DEBUG] Sending request to: {direct_url}")
            response = requests.get(direct_url, stream=True, headers=headers, timeout=30)
            print(f"[DEBUG] Response status: {response.status_code}")

            if response.status_code == 200:
                content_length = response.headers.get('content-length')
                if content_length:
                    print(f"[DEBUG] File size: {int(content_length)} bytes")

                with open(image_path, "wb") as file:
                    downloaded = 0
                    for chunk in response.iter_content(1024):
                        if chunk:
                            file.write(chunk)
                            downloaded += len(chunk)

                print(f"[DEBUG] Downloaded {downloaded} bytes")

                # Kiểm tra file size
                if os.path.exists(image_path):
                    file_size = os.path.getsize(image_path)
                    print(f"[DEBUG] File saved with size: {file_size} bytes")

                    if file_size > 0:
                        image_paths.append(os.path.abspath(image_path))
                        # Lưu file ID để xóa sau (nếu có)
                        if 'id' in img_info:
                            file_ids.append(img_info['id'])
                        print(f"[SUCCESS] Tải ảnh thành công: {image_name} ({file_size} bytes)")
                    else:
                        print(f"[ERROR] File rỗng: {image_name}")
                        os.remove(image_path)
                else:
                    print(f"[ERROR] File không được tạo: {image_path}")
            else:
                print(f"[ERROR] Không thể tải: {url} - Mã {response.status_code}")
                print(f"[ERROR] Response text: {response.text[:200]}")

        except Exception as e:
            print(f"[ERROR] Lỗi khi tải ảnh {url}: {e}")
            import traceback
            traceback.print_exc()

    print(f"[INFO] Hoàn thành download. Thành công: {len(image_paths)}/{len(images)} ảnh")
    return image_paths, file_ids

def download_images(image_urls, save_folder="image"):
    """Backward compatibility function"""
    save_folder = os.path.normpath(save_folder)
    if not os.path.exists(save_folder):
        os.makedirs(save_folder)
        print(f"[INFO] Tạo folder: {save_folder}")

    image_paths = []
    print(f"[INFO] Bắt đầu download {len(image_urls)} ảnh...")

    for i, url in enumerate(image_urls, 1):
        url = url.strip()
        if not url:
            continue

        print(f"[INFO] Đang download ảnh {i}/{len(image_urls)}: {url}")
        direct_url = get_drive_direct_link(url)
        print(f"[DEBUG] Direct URL: {direct_url}")

        # Generate a safe filename based on the file ID
        if "drive.google.com" in url:
            file_id_match = re.search(r"[/=]([a-zA-Z0-9_-]{28,})", url)
            if file_id_match:
                image_name = f"image_{file_id_match.group(1)[:10]}.jpg"
            else:
                image_name = f"image_{int(time.time())}_{i}.jpg"
        else:
            image_name = os.path.basename(direct_url).split("?")[0]
            # Làm sạch tên file
            image_name = re.sub(r'[<>:"/\\|?*]', '_', image_name)
            image_name = re.sub(r'[^\w\-_\.]', '_', image_name)
            if not image_name.lower().endswith((".jpg", ".jpeg", ".png", ".gif", ".webp")):
                image_name += ".jpg"

        image_path = os.path.join(save_folder, image_name)
        print(f"[DEBUG] Saving to: {image_path}")

        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }

            print(f"[DEBUG] Sending request to: {direct_url}")
            response = requests.get(direct_url, stream=True, headers=headers, timeout=30)
            print(f"[DEBUG] Response status: {response.status_code}")

            if response.status_code == 200:
                content_length = response.headers.get('content-length')
                if content_length:
                    print(f"[DEBUG] File size: {int(content_length)} bytes")

                with open(image_path, "wb") as file:
                    downloaded = 0
                    for chunk in response.iter_content(1024):
                        if chunk:
                            file.write(chunk)
                            downloaded += len(chunk)

                print(f"[DEBUG] Downloaded {downloaded} bytes")

                # Kiểm tra file size
                if os.path.exists(image_path):
                    file_size = os.path.getsize(image_path)
                    print(f"[DEBUG] File saved with size: {file_size} bytes")

                    if file_size > 0:
                        image_paths.append(os.path.abspath(image_path))
                        print(f"[SUCCESS] Tải ảnh thành công: {image_name} ({file_size} bytes)")
                    else:
                        print(f"[ERROR] File rỗng: {image_name}")
                        os.remove(image_path)
                else:
                    print(f"[ERROR] File không được tạo: {image_path}")
            else:
                print(f"[ERROR] Không thể tải: {url} - Mã {response.status_code}")
                print(f"[ERROR] Response text: {response.text[:200]}")

        except Exception as e:
            print(f"[ERROR] Lỗi khi tải ảnh {url}: {e}")
            import traceback
            traceback.print_exc()

    print(f"[INFO] Hoàn thành download. Thành công: {len(image_paths)}/{len(image_urls)} ảnh")
    return image_paths


# ───── LOGIN COOKIE ─────────────────────────────────────────────────────────────

def login_with_cookies(driver, group_url, cookie_file):
    print(f"[INFO] Đang đăng nhập với cookie: {cookie_file}")
    try:
        with open(cookie_file, "r", encoding="utf-8") as f:
            cookies = json.load(f)

        driver.get("https://www.facebook.com")
        WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.TAG_NAME, "body")))

        for cookie in cookies:
            cookie.pop("storeId", None)
            cookie.pop("id", None)
            if "sameSite" in cookie and cookie["sameSite"].lower() in ["no_restriction", "unspecified"]:
                cookie["sameSite"] = "None"
            try:
                driver.add_cookie(cookie)
            except Exception as e:
                print(f"[WARNING] Cookie lỗi: {cookie.get('name')}, {e}")

        driver.get(group_url)
        WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.TAG_NAME, "body")))

        if "login" in driver.current_url:
            print("[ERROR] Cookie sai hoặc hết hạn!")
            return False

        print("[INFO] Đăng nhập thành công ")
        return True

    except Exception as e:
        print(f"[ERROR] Đăng nhập thất bại: {e}")
        return False

# ───── UPLOAD ẢNH ───────────────────────────────────────────────────────────────

def upload_images_from_drive(driver, image_data):
    """Upload ảnh từ Google Drive data (format mới) bằng cách download vào folder image và upload"""
    try:
        if not image_data or not image_data.get('images'):
            print("[INFO] Không có ảnh để upload")
            return True, []

        images = image_data['images']
        print(f"[INFO] Đang upload {len(images)} ảnh từ Google Drive...")

        # Download ảnh và lấy file IDs để xóa sau
        image_paths, file_ids = download_images_from_data(image_data, save_folder="image")

        if not image_paths:
            print("[ERROR] Không có ảnh nào được download thành công!")
            return False, []

        print(f"[INFO] Đã download {len(image_paths)} ảnh vào folder image:")
        for path in image_paths:
            print(f"  - {os.path.basename(path)} ({os.path.getsize(path)} bytes)")

        # Tối ưu tìm nút thêm ảnh/video
        print("[INFO] Đang tìm nút thêm ảnh/video...")
        add_btn = find_photo_video_button_fast(driver)

        if not add_btn:
            print("[ERROR] Không tìm thấy nút thêm ảnh/video với priority selectors!")
            print("[DEBUG] Hiển thị tất cả buttons có thể để debug:")

            # Enhanced debugging - show all possible buttons
            try:
                all_buttons = driver.find_elements(By.XPATH, "//div[@role='button'] | //button | //a[@role='button']")
                print(f"[DEBUG] Tìm thấy {len(all_buttons)} button elements")

                # Show buttons that might contain photo/video keywords
                photo_related_buttons = []
                for i, btn in enumerate(all_buttons):
                    try:
                        aria_label = btn.get_attribute('aria-label') or ''
                        text_content = btn.text or ''
                        class_name = btn.get_attribute('class') or ''

                        # Check if button contains photo/video related keywords
                        all_text = f"{aria_label} {text_content} {class_name}".lower()
                        if any(keyword in all_text for keyword in ['ảnh', 'video', 'photo', 'image', 'media', 'upload']):
                            photo_related_buttons.append({
                                'index': i,
                                'aria_label': aria_label[:50],
                                'text': text_content[:50],
                                'class': class_name[:50]
                            })
                    except:
                        continue

                print(f"[DEBUG] Tìm thấy {len(photo_related_buttons)} buttons có liên quan đến ảnh/video:")
                for btn_info in photo_related_buttons[:10]:  # Show first 10
                    print(f"[DEBUG] Button {btn_info['index']}: aria-label='{btn_info['aria_label']}', text='{btn_info['text']}', class='{btn_info['class']}'")

            except Exception as e:
                print(f"[DEBUG] Error during button analysis: {e}")

            print("[DEBUG] Thử tìm nút bằng smart detection...")
            add_btn = find_photo_video_button_smart(driver)

            if not add_btn:
                print("[ERROR] Hoàn toàn không tìm thấy nút thêm ảnh/video!")
                return False, file_ids
            else:
                print("[SUCCESS] Tìm thấy nút thêm ảnh/video bằng smart detection!")

        # Click ICON ảnh/video với retry mechanism tối ưu
        click_success = click_button_fast(driver, add_btn, "icon ảnh/video")

        if not click_success:
            print("[ERROR] Không thể click icon ảnh/video sau 3 lần thử!")
            return False, file_ids

        # Bước 2: Chờ dialog xuất hiện và click ô upload lớn
        time.sleep(0.2)  # Giảm từ 0.3s xuống 0.2s

        add_option = find_add_photos_videos_option(driver)
        if not add_option:
            print("[ERROR] Không tìm thấy ô upload lớn")
            return False, file_ids

        # Click ô upload lớn
        if not click_button_fast(driver, add_option, "ô upload lớn"):
            print("[ERROR] Không thể click ô upload lớn")
            return False, file_ids

        # BƯỚC QUAN TRỌNG: Bấm nút "Thêm" trong dialog (tối ưu tốc độ)
        add_button_clicked = click_add_button_in_dialog_fast(driver)

        if not add_button_clicked:
            print("[ERROR] Không thể bấm nút 'Thêm' trong dialog!")
            return False, file_ids

        # Chờ file dialog xuất hiện (tối ưu thời gian)
        time.sleep(1)  # Giảm từ 1.5s xuống 1s

        # Upload từ folder image với improved method (bao gồm Ctrl+A và Open)
        upload_success = upload_files_to_facebook_with_select_all(driver, image_paths)

        return upload_success, file_ids

    except Exception as e:
        print(f"[ERROR] Lỗi khi upload ảnh từ Drive: {e}")
        import traceback
        traceback.print_exc()
        return False, []

def upload_images_from_local_folder(driver):
    """Upload ảnh trực tiếp từ thư mục local image"""
    try:
        # Đường dẫn cố định đến thư mục image - FIX ENCODING
        local_image_folder = os.path.normpath(r"C:\Users\<USER>\Documents\Zalo Received Files\gift\gift\image")
        print(f"[INFO] Đang upload ảnh từ thư mục local: {local_image_folder}")
        print(f"[DEBUG] Normalized path: {repr(local_image_folder)}")

        # Kiểm tra thư mục có tồn tại không
        if not os.path.exists(local_image_folder):
            print(f"[ERROR] Thư mục không tồn tại: {local_image_folder}")
            # Try alternative paths
            alt_paths = [
                r"C:\Users\<USER>\Documents\Zalo Received Files\gift\gift\image",
                os.path.join(os.getcwd(), "image"),
                "image"
            ]
            for alt_path in alt_paths:
                if os.path.exists(alt_path):
                    local_image_folder = os.path.normpath(alt_path)
                    print(f"[INFO] Using alternative path: {local_image_folder}")
                    break
            else:
                return False, []

        # Lấy danh sách file ảnh
        image_extensions = ('.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff', '.tif')
        image_files = [f for f in os.listdir(local_image_folder)
                      if f.lower().endswith(image_extensions)]

        if not image_files:
            print(f"[ERROR] Không có file ảnh nào trong thư mục: {local_image_folder}")
            return False, []

        print(f"[INFO] Tìm thấy {len(image_files)} file ảnh:")
        for img in image_files[:5]:  # Show first 5
            print(f"  - {img}")
        if len(image_files) > 5:
            print(f"  ... và {len(image_files) - 5} file khác")

        # Tạo đường dẫn đầy đủ cho các file
        image_paths = [os.path.join(local_image_folder, img) for img in image_files]

        # Tối ưu tìm nút thêm ảnh/video
        print("[INFO] Đang tìm nút thêm ảnh/video...")
        add_btn = find_photo_video_button_fast(driver)

        if not add_btn:
            print("[ERROR] Không tìm thấy nút thêm ảnh/video với các selector chuẩn!")
            print("[DEBUG] Thử tìm nút bằng cách phân tích tất cả buttons...")

            # Try to find button by analyzing all buttons
            add_btn = find_photo_video_button_smart(driver)

            if not add_btn:
                print("[ERROR] Hoàn toàn không tìm thấy nút thêm ảnh/video!")
                return False, []
            else:
                print("[SUCCESS] Tìm thấy nút thêm ảnh/video bằng smart detection!")

        # Click ICON ảnh/video với retry mechanism tối ưu
        click_success = click_button_fast(driver, add_btn, "icon ảnh/video")

        if not click_success:
            print("[ERROR] Không thể click icon ảnh/video sau 3 lần thử!")
            return False, []

        # Bước 2: Chờ dialog xuất hiện và click ô upload lớn
        print("[INFO] Chờ dialog xuất hiện sau khi click icon...")
        time.sleep(1)  # Chờ dialog upload xuất hiện

        add_option = find_add_photos_videos_option(driver)
        if not add_option:
            print("[ERROR] Không tìm thấy ô upload lớn với background-image G0Ewc9xH897.png")
            return False, []

        # Click ô upload lớn
        if not click_button_fast(driver, add_option, "ô upload lớn"):
            print("[ERROR] Không thể click ô upload lớn")
            return False, []

        # Chờ dialog ngắn hơn
        time.sleep(0.5)  # Giảm từ 1s xuống 0.5s

        # BƯỚC QUAN TRỌNG: Bấm nút "Thêm" trong dialog (tối ưu tốc độ)
        add_button_clicked = click_add_button_in_dialog_fast(driver)

        if not add_button_clicked:
            print("[ERROR] Không thể bấm nút 'Thêm' trong dialog!")
            return False, []

        # Chờ file dialog xuất hiện (tối ưu thời gian)
        time.sleep(0.8)  # Giảm từ 1s xuống 0.8s

        # Upload từ thư mục local với improved method (bao gồm Ctrl+A và Open)
        upload_success = upload_files_to_facebook_with_select_all(driver, image_paths)

        return upload_success, []  # Không có file IDs để xóa vì không phải từ Google Drive

    except Exception as e:
        print(f"[ERROR] Lỗi khi upload ảnh từ thư mục local: {e}")
        import traceback
        traceback.print_exc()
        return False, []

def navigate_and_select_all_files_fast(image_folder):
    """Navigate đến thư mục và chọn tất cả file với Ctrl+A, sau đó bấm Open"""
    try:
        print(f"[INFO] Navigate và chọn tất cả file trong: {image_folder}")


        # Method 1: Navigate to folder using Ctrl+L
        try:
            print("[INFO] Bấm Ctrl+L để focus address bar...")
            pyautogui.hotkey('ctrl', 'l')

            print(f"[INFO] Gõ đường dẫn: {image_folder}")
            pyautogui.typewrite(image_folder, interval=0.01)  # Fast typing

            print("[INFO] Bấm Enter để navigate...")
            pyautogui.press('enter')

        except Exception as e:
            print(f"[WARNING] Navigate method 1 failed: {e}")
            # Fallback: try typing path directly
            try:
                # pyautogui.typewrite(image_folder, interval=0.01)
                # pyautogui.press('enter')
            except:
                pass

        # Click vào vùng file list để đảm bảo focus đúng
        print("[INFO] Click vào vùng file list để focus...")
        # Click vào giữa màn hình (vùng file list)
        screen_width, screen_height = pyautogui.size()
        center_x = screen_width // 2
        center_y = screen_height // 2
        pyautogui.click(center_x, center_y)

        # Select all files with Ctrl+A
        print("[INFO] Bấm Ctrl+A để chọn tất cả file...")
        pyautogui.hotkey('ctrl', 'a')

        # Press Enter to open (most reliable method)
        print("[INFO] Bấm Enter để Open tất cả file...")
        pyautogui.press('enter')

        print("[SUCCESS] Đã hoàn thành navigate và select all files!")
        return True

    except Exception as e:
        print(f"[ERROR] Navigate and select all failed: {e}")
        return False

def click_button_fast(driver, button, button_name):
    """Click button với retry mechanism tối ưu tốc độ"""
    try:
        print(f"[INFO] Click {button_name} (fast mode)...")

        # Fast click without scroll first
        try:
            button.click()
            print(f"[SUCCESS] Đã click {button_name} (fast mode)")
            return True
        except Exception as e:
            print(f"[WARNING] Fast click failed: {e}, trying with scroll...")

            # Fallback with scroll
            try:
                driver.execute_script("arguments[0].scrollIntoView(true);", button)
                time.sleep(0.3)  # Minimal wait
                button.click()
                print(f"[SUCCESS] Đã click {button_name} (with scroll)")
                return True
            except Exception as e2:
                print(f"[ERROR] Both click methods failed for {button_name}: {e2}")
                return False

    except Exception as e:
        print(f"[ERROR] Click {button_name} failed: {e}")
        return False

def verify_create_post_dialog(driver):
    """Verify rằng dialog 'Tạo bài viết' đã mở - tối ưu tốc độ"""
    try:
        dialog_selectors = [
            "//div[@role='dialog'][contains(@aria-label, 'Tạo bài viết')]",
            "//div[@role='dialog'][contains(@aria-label, 'Create post')]",
            "//div[@role='dialog']//span[contains(text(), 'Tạo bài viết')]"
        ]

        for selector in dialog_selectors:
            try:
                dialog = WebDriverWait(driver, 0.5).until(
                    EC.presence_of_element_located((By.XPATH, selector))
                )
                if dialog.is_displayed():
                    return True
            except:
                continue
        return False
    except:
        return False



def find_add_photos_videos_option(driver):
    """Tìm và click ô upload lớn với background-image G0Ewc9xH897.png"""
    try:
        print("[INFO] Tìm ô upload lớn với background-image G0Ewc9xH897.png...")

        # Selectors cho ô upload lớn với background-image G0Ewc9xH897.png
        option_selectors = [
            # Target chính xác element với background-image G0Ewc9xH897.png
            "//i[contains(@style, 'G0Ewc9xH897.png')]/parent::div/parent::div",
            "//i[contains(@style, 'G0Ewc9xH897.png')]/ancestor::div[@class]",
            "//div[.//i[contains(@style, 'G0Ewc9xH897.png')]]",

            # Target theo class patterns của element bạn cung cấp
            "//div[@class='x9f619 x1n2onr6 x1ja2u2z x78zum5 xdt5ytf x2lah0s x193iq5w x6s0dn4 x1gslohp x12nagc xzboxd6 x14l7nz5']",
            "//div[contains(@class, 'x9f619') and contains(@class, 'x1n2onr6') and contains(@class, 'x1ja2u2z')]",

            # Target theo structure - div chứa i element với background-image
            "//div[contains(@class, 'x9f619')]//i[contains(@style, 'background-image')]",
            "//div[contains(@class, 'x9f619')]//i[@data-visualcompletion='css-img']",
            "//div//i[contains(@style, 'G0Ewc9xH897.png')]/parent::div",

            # Target theo size và style của icon upload
            "//i[@style and contains(@style, 'width: 20px; height: 20px')]/parent::div/parent::div",
            "//i[contains(@style, 'background-position: -25px -108px')]/parent::div/parent::div",

            # Target theo class của inner div
            "//div[@class='html-div xdj266r x11i5rnm xat24cr x1mh8g0r xexx8yu x4uap5 x18d9i69 xkhd6sd x14yjl9h xudhj91 x18nykt9 xww2gxu x6s0dn4 x972fbf xcfux6l x1qhh985 xm0m39n x9f619 x3nfvp2 xl56j7k x1n2onr6 x1qhmfi1 x1vqgdyp x100vrsf']",
            "//div[contains(@class, 'html-div') and contains(@class, 'xdj266r')]",

            # Generic selectors cho upload area
            "//div[contains(@class, 'x9f619')][@role='button' or @tabindex]",
            "//div[contains(@class, 'x9f619') and .//i[@data-visualcompletion]]",

            # Fallback - tìm bất kỳ div nào có icon upload
            "//div[.//i[contains(@style, 'background-image')]]",
            "//div[.//i[@data-visualcompletion='css-img']]",

            # Text-based fallbacks
            "//div[contains(text(), 'Thêm ảnh/video')]",
            "//div[contains(text(), 'Add photos/videos')]",
            "//div[contains(text(), 'hoặc kéo và thả')]",
            "//div[contains(text(), 'or drag and drop')]"
        ]

        for i, selector in enumerate(option_selectors, 1):
            try:
                option = WebDriverWait(driver, 1).until(
                    EC.element_to_be_clickable((By.XPATH, selector))
                )
                print(f"[SUCCESS] Tìm thấy ô upload lớn với selector {i}")
                return option
            except:
                continue

        print("[ERROR] Không tìm thấy ô upload lớn với background-image G0Ewc9xH897.png")
        return None

    except Exception as e:
        print(f"[ERROR] Find upload area failed: {e}")
        return None

def find_photo_video_button_fast(driver):
    """Tìm nút thêm ảnh/video với tốc độ tối ưu"""
    try:
        print("[INFO] Tìm nút thêm ảnh/video (fast mode)...")

        # Bước 1: Verify dialog "Tạo bài viết" đã mở
        if not verify_create_post_dialog(driver):
            print("[ERROR] Dialog 'Tạo bài viết' chưa mở - không thể tìm nút thêm ảnh/video")
            return None

        # Selectors cho ICON ảnh/video TRONG dialog "Tạo bài viết" - Target chính xác Facebook icon
        priority_selectors = [
            # Tìm theo Facebook icon image - CHÍNH XÁC NHẤT
            "//div[@role='dialog']//img[contains(@src, 'Ivw7nhRtXyo.png')]/parent::*[@role='button']",
            "//div[@role='dialog']//img[contains(@src, 'Ivw7nhRtXyo.png')]/ancestor::*[@role='button']",
            "//div[@role='dialog']//*[@role='button'][.//img[contains(@src, 'Ivw7nhRtXyo.png')]]",

            # Tìm theo class của Facebook icon
            "//div[@role='dialog']//img[contains(@class, 'x1b0d499')]/parent::*[@role='button']",
            "//div[@role='dialog']//img[contains(@class, 'x1b0d499')]/ancestor::*[@role='button']",
            "//div[@role='dialog']//*[@role='button'][.//img[contains(@class, 'x1b0d499')]]",

            # Tìm theo size 24x24 của icon
            "//div[@role='dialog']//img[@style='height: 24px; width: 24px;']/parent::*[@role='button']",
            "//div[@role='dialog']//img[@style='height: 24px; width: 24px;']/ancestor::*[@role='button']",
            "//div[@role='dialog']//*[@role='button'][.//img[@style='height: 24px; width: 24px;']]",

            # Tìm theo Facebook resource domain
            "//div[@role='dialog']//img[contains(@src, 'static.xx.fbcdn.net')]/parent::*[@role='button']",
            "//div[@role='dialog']//img[contains(@src, 'static.xx.fbcdn.net')]/ancestor::*[@role='button']",
            "//div[@role='dialog']//*[@role='button'][.//img[contains(@src, 'static.xx.fbcdn.net')]]",

            # Generic image-based selectors
            "//div[@role='dialog']//*[@role='button'][.//img]",
            "//div[@role='dialog']//img/parent::*[@role='button']",
            "//div[@role='dialog']//img/ancestor::*[@role='button']",

            # SVG fallbacks (nếu không phải img)
            "//div[@role='dialog']//div[@role='button'][.//*[name()='svg']][@aria-label]",
            "//div[@role='dialog']//div[@role='button'][.//*[name()='svg']][contains(@aria-label, 'ảnh')]",
            "//div[@role='dialog']//div[@role='button'][.//*[name()='svg']][contains(@aria-label, 'photo')]",
            "//div[@role='dialog']//div[@role='button'][.//*[name()='svg']][contains(@aria-label, 'image')]",
            "//div[@role='dialog']//div[@role='button'][.//*[name()='svg']]",

            # Text-based fallbacks
            "//div[@role='dialog']//div[contains(text(), 'Thêm ảnh/video')]",
            "//div[@role='dialog']//div[contains(text(), 'Add photos/videos')]",

            # Global fallbacks
            "//div[@aria-label='Thêm ảnh và video']",
            "//div[@aria-label='Add photos and videos']"
        ]

        for i, selector in enumerate(priority_selectors, 1):
            try:
                add_btn = WebDriverWait(driver, 0.5).until(  # Very fast timeout
                    EC.element_to_be_clickable((By.XPATH, selector))
                )
                print(f"[SUCCESS] Tìm thấy nút với priority selector {i}: {selector}")
                return add_btn
            except:
                continue

        print("[WARNING] Priority selectors failed, trying smart detection...")
        return find_photo_video_button_smart(driver)

    except Exception as e:
        print(f"[ERROR] Fast detection failed: {e}")
        return None

def find_photo_video_button_smart(driver):
    """Smart detection of photo/video button by analyzing all buttons"""
    try:
        print("[INFO] Phân tích thông minh tất cả buttons để tìm nút ảnh/video...")

        # Get all clickable elements that could be the photo/video button
        all_buttons = driver.find_elements(By.XPATH, "//div[@role='button'] | //button | //a[@role='button']")
        print(f"[DEBUG] Tìm thấy {len(all_buttons)} button elements để phân tích")

        # Keywords to look for (Vietnamese and English)
        photo_keywords = [
            'ảnh', 'video', 'thêm ảnh', 'ảnh và video', 'thêm ảnh và video',
            'photo', 'photos', 'add photo', 'add photos', 'photo/video',
            'photos and videos', 'add photos and videos', 'media', 'upload'
        ]

        # Score each button based on how likely it is to be the photo/video button
        candidates = []

        for i, btn in enumerate(all_buttons):
            try:
                score = 0
                reasons = []

                # Get button attributes
                aria_label = (btn.get_attribute('aria-label') or '').lower()
                text_content = (btn.text or '').lower()
                class_name = (btn.get_attribute('class') or '').lower()
                title = (btn.get_attribute('title') or '').lower()

                # Check for photo/video keywords in various attributes
                all_text = f"{aria_label} {text_content} {class_name} {title}"

                for keyword in photo_keywords:
                    if keyword in all_text:
                        if keyword in ['thêm ảnh và video', 'add photos and videos']:
                            score += 100  # Exact match gets highest score
                            reasons.append(f"Exact match: '{keyword}'")
                        elif keyword in ['ảnh/video', 'photo/video']:
                            score += 80
                            reasons.append(f"High match: '{keyword}'")
                        elif keyword in ['thêm ảnh', 'add photo', 'add photos']:
                            score += 60
                            reasons.append(f"Good match: '{keyword}'")
                        elif keyword in ['ảnh', 'photo', 'photos']:
                            score += 40
                            reasons.append(f"Basic match: '{keyword}'")
                        elif keyword in ['video', 'media', 'upload']:
                            score += 20
                            reasons.append(f"Related match: '{keyword}'")

                # Bonus points for being visible and enabled
                try:
                    if btn.is_displayed():
                        score += 10
                        reasons.append("Visible")
                    if btn.is_enabled():
                        score += 10
                        reasons.append("Enabled")
                except:
                    pass

                # Penalty for certain classes that indicate it's not the right button
                penalty_keywords = ['close', 'cancel', 'back', 'đóng', 'hủy', 'quay lại']
                for penalty in penalty_keywords:
                    if penalty in all_text:
                        score -= 50
                        reasons.append(f"Penalty: '{penalty}'")

                if score > 0:
                    candidates.append({
                        'element': btn,
                        'score': score,
                        'reasons': reasons,
                        'aria_label': aria_label,
                        'text': text_content,
                        'index': i
                    })

            except Exception as e:
                continue

        # Sort candidates by score (highest first)
        candidates.sort(key=lambda x: x['score'], reverse=True)

        print(f"[DEBUG] Tìm thấy {len(candidates)} ứng viên cho nút ảnh/video:")
        for i, candidate in enumerate(candidates[:5]):  # Show top 5
            print(f"[DEBUG] Candidate {i+1}: Score={candidate['score']}, "
                  f"aria-label='{candidate['aria_label'][:30]}', "
                  f"text='{candidate['text'][:30]}', "
                  f"reasons={candidate['reasons']}")

        # Return the best candidate if it has a reasonable score
        if candidates and candidates[0]['score'] >= 40:
            best_candidate = candidates[0]
            print(f"[SUCCESS] Chọn button với score {best_candidate['score']}: {best_candidate['reasons']}")
            return best_candidate['element']
        else:
            print("[WARNING] Không tìm thấy ứng viên phù hợp (score < 40)")
            return None

    except Exception as e:
        print(f"[ERROR] Smart detection failed: {e}")
        return None

def click_add_button_in_dialog_fast(driver):
    """Bấm nút 'Thêm' trong dialog 'Thêm ảnh/video' - phiên bản tối ưu tốc độ"""
    try:
        # Prioritized selectors - most common first for speed
        priority_selectors = [
            "//span[text()='Thêm']/parent::div[@role='button']",
            "//div[@role='button'][.//span[text()='Thêm']]",
            "//span[text()='Add']/parent::div[@role='button']",
            "//div[@role='button'][.//span[text()='Add']]"
        ]

        for selector in priority_selectors:
            try:
                add_button = WebDriverWait(driver, 0.3).until(  # Giảm xuống 0.3s
                    EC.element_to_be_clickable((By.XPATH, selector))
                )
                add_button.click()
                return True
            except:
                continue

        return click_add_button_in_dialog(driver)

    except:
        return False

def click_add_button_in_dialog(driver):
    """Bấm nút 'Thêm' trong dialog 'Thêm ảnh/video'"""
    try:
        print("[INFO] Tìm nút 'Thêm' trong dialog...")

        # Multiple selectors for the "Thêm" button in dialog
        add_button_selectors = [
            # Vietnamese
            "//span[text()='Thêm']/parent::div[@role='button']",
            "//div[@role='button'][.//span[text()='Thêm']]",
            "//button[.//span[text()='Thêm']]",
            "//div[contains(@class, 'button') and .//span[text()='Thêm']]",
            "//div[@role='button'][contains(., 'Thêm')]",

            # English
            "//span[text()='Add']/parent::div[@role='button']",
            "//div[@role='button'][.//span[text()='Add']]",
            "//button[.//span[text()='Add']]",
            "//div[contains(@class, 'button') and .//span[text()='Add']]",
            "//div[@role='button'][contains(., 'Add')]",

            # Generic selectors in dialog context
            "//div[@role='dialog']//div[@role='button'][.//span[text()='Thêm' or text()='Add']]",
            "//div[contains(@aria-label, 'dialog')]//div[@role='button'][.//span[text()='Thêm' or text()='Add']]"
        ]

        add_button = None
        for i, selector in enumerate(add_button_selectors, 1):
            try:
                add_button = WebDriverWait(driver, 1).until(  # Giảm từ 3s xuống 1s
                    EC.element_to_be_clickable((By.XPATH, selector))
                )
                print(f"[SUCCESS] Tìm thấy nút 'Thêm' với selector {i}")
                break
            except:
                continue

        if not add_button:
            print("[ERROR] Không tìm thấy nút 'Thêm' trong dialog!")

            # Debug: Show all buttons in dialog
            try:
                print("[DEBUG] Tìm tất cả buttons trong dialog:")
                dialog_buttons = driver.find_elements(By.XPATH, "//div[@role='dialog']//div[@role='button'] | //div[@role='dialog']//button")
                print(f"[DEBUG] Tìm thấy {len(dialog_buttons)} buttons trong dialog:")
                for i, btn in enumerate(dialog_buttons[:10]):
                    try:
                        text = btn.text[:30] if btn.text else 'No text'
                        aria_label = btn.get_attribute('aria-label') or 'No aria-label'
                        print(f"[DEBUG] Dialog Button {i}: text='{text}', aria-label='{aria_label}'")
                    except:
                        pass
            except:
                pass

            return False

        # Click the "Thêm" button with retry
        try:
            driver.execute_script("arguments[0].scrollIntoView(true);", add_button)
            time.sleep(0.3)  # Giảm từ 0.5s xuống 0.3s
            add_button.click()
            return True
        except:
            try:
                time.sleep(0.5)
                add_button.click()
                return True
            except:
                return False

    except Exception as e:
        print(f"[ERROR] Lỗi khi bấm nút 'Thêm': {e}")
        return False



def upload_files_to_facebook_with_select_all(driver, image_paths):
    """Upload method với Ctrl+A để chọn tất cả file và bấm Open"""
    try:
        print(f"[INFO] Đang upload {len(image_paths)} file lên Facebook (với Ctrl+A và Open)...")

        # Validate image paths first
        valid_paths = []
        for path in image_paths:
            abs_path = os.path.abspath(path)
            if os.path.exists(abs_path) and os.path.getsize(abs_path) > 0:
                valid_paths.append(abs_path)
                print(f"[VALID] {os.path.basename(abs_path)} ({os.path.getsize(abs_path)} bytes)")
            else:
                print(f"[INVALID] {abs_path} - file không tồn tại hoặc rỗng")

        if not valid_paths:
            print("[ERROR] Không có file hợp lệ để upload!")
            return False

        print(f"[INFO] Sẽ upload {len(valid_paths)} file hợp lệ...")

        # Sử dụng pyautogui để navigate và chọn tất cả file (tối ưu tốc độ)
        navigate_success = navigate_and_select_all_files_fast(image_folder=os.path.dirname(valid_paths[0]) if valid_paths else "image")

        if not navigate_success:
            print("[WARNING] Navigate failed, thử fallback method...")
            return upload_files_to_facebook_improved(driver, image_paths)




        # Verify upload success
        upload_success = verify_upload_success(driver, len(valid_paths))

        if upload_success:
            print("[SUCCESS] Upload thành công với Ctrl+A method!")
            return True
        else:
            print("[WARNING] Không thấy ảnh trong UI, thử fallback method...")
            return upload_files_to_facebook_improved(driver, image_paths)

    except Exception as e:
        print(f"[ERROR] Lỗi khi upload với Ctrl+A method: {e}")
        print("[INFO] Thử fallback method...")
        return upload_files_to_facebook_improved(driver, image_paths)

def upload_files_to_facebook_improved(driver, image_paths):
    """Improved upload method with better error handling and verification"""
    try:
        print(f"[INFO] Đang upload {len(image_paths)} file lên Facebook (improved method)...")

        # Validate image paths first
        valid_paths = []
        for path in image_paths:
            abs_path = os.path.abspath(path)
            if os.path.exists(abs_path) and os.path.getsize(abs_path) > 0:
                valid_paths.append(abs_path)
                print(f"[VALID] {os.path.basename(abs_path)} ({os.path.getsize(abs_path)} bytes)")
            else:
                print(f"[INVALID] {abs_path} - file không tồn tại hoặc rỗng")

        if not valid_paths:
            print("[ERROR] Không có file hợp lệ để upload!")
            return False

        print(f"[INFO] Sẽ upload {len(valid_paths)} file hợp lệ...")

        # Enhanced file input detection
        print("[INFO] Đang tìm file input...")
        file_input = None

        # Multiple strategies to find file input
        input_strategies = [
            ("Direct file input", "//input[@type='file']"),
            ("Accept image input", "//input[@accept*='image']"),
            ("Accept all input", "//input[contains(@accept, '*')]"),
            ("Hidden file input", "//input[@type='file' and @style]"),
            ("Any file input", "//input[contains(@accept, 'image/*')]")
        ]

        for strategy_name, selector in input_strategies:
            try:
                print(f"[DEBUG] Thử {strategy_name}: {selector}")
                file_inputs = driver.find_elements(By.XPATH, selector)
                if file_inputs:
                    # Find the most suitable input (visible and interactable)
                    for inp in file_inputs:
                        try:
                            if inp.is_enabled():
                                file_input = inp
                                print(f"[SUCCESS] Tìm thấy file input với {strategy_name}")
                                break
                        except:
                            continue
                    if file_input:
                        break
            except Exception as e:
                print(f"[DEBUG] {strategy_name} failed: {e}")
                continue

        if not file_input:
            print("[ERROR] Không tìm thấy file input!")
            print("[DEBUG] Thử tìm tất cả input elements:")
            try:
                all_inputs = driver.find_elements(By.TAG_NAME, "input")
                print(f"[DEBUG] Tìm thấy {len(all_inputs)} input elements")
                for i, inp in enumerate(all_inputs[:5]):
                    try:
                        input_type = inp.get_attribute('type') or 'No type'
                        accept_attr = inp.get_attribute('accept') or 'No accept'
                        print(f"[DEBUG] Input {i}: type='{input_type}', accept='{accept_attr}'")
                    except:
                        pass
            except:
                pass
            return False

        # Set multiple attribute if not present
        try:
            has_multiple = driver.execute_script("return arguments[0].hasAttribute('multiple');", file_input)
            if not has_multiple:
                driver.execute_script("arguments[0].setAttribute('multiple', 'multiple');", file_input)
                print("[INFO] Đã đặt thuộc tính multiple cho file input")
        except Exception as e:
            print(f"[WARNING] Không thể set multiple attribute: {e}")

        # Method 1: Upload all files at once
        upload_success = False
        try:
            print("[INFO] Method 1: Upload tất cả file cùng lúc...")

            # Join all file paths with newline for multiple file selection
            all_files = "\n".join(valid_paths)
            print(f"[DEBUG] Uploading {len(valid_paths)} files...")

            # Clear and send files
            file_input.clear()
            file_input.send_keys(all_files)

            print("[INFO] Đã gửi file paths, đang chờ xử lý...")

            # Wait ngắn hơn cho upload processing
            wait_time = min(10, 3 + len(valid_paths))  # Giảm thời gian chờ đáng kể
            print(f"[INFO] Chờ {wait_time}s để xử lý {len(valid_paths)} ảnh...")
            time.sleep(wait_time)

            # Enhanced upload verification
            upload_success = verify_upload_success(driver, len(valid_paths))

            if upload_success:
                print("[SUCCESS] Method 1: Upload thành công!")
                return True
            else:
                print("[WARNING] Method 1: Không thấy ảnh trong UI sau khi upload")

        except Exception as e:
            print(f"[WARNING] Method 1 thất bại: {e}")

        # Method 2: Upload files one by one if Method 1 failed
        if not upload_success:
            try:
                print("[INFO] Method 2: Upload từng file một...")

                # Clear previous attempts
                file_input.clear()
                time.sleep(0.3)  # Giảm từ 0.5s xuống 0.3s

                # Upload first file
                first_file = valid_paths[0]
                file_input.send_keys(first_file)
                time.sleep(0.5)  # Giảm từ 1s xuống 0.5s

                # For additional files, try to add them
                for i, file_path in enumerate(valid_paths[1:], 1):
                    time.sleep(0.5)  # Giảm từ 1s xuống 0.5s
                    current_inputs = driver.find_elements(By.XPATH, "//input[@type='file']")

                    if len(current_inputs) > i:
                        current_inputs[i].send_keys(file_path)
                    else:
                        add_more_success = try_add_more_files(driver, file_path)
                        if not add_more_success:
                            break

                # Wait for processing ngắn hơn
                time.sleep(2)  # Giảm từ 4s xuống 2s

                # Verify upload
                upload_success = verify_upload_success(driver, min(len(valid_paths), i + 1))

                if upload_success:
                    print("[SUCCESS] Method 2: Upload thành công!")
                    return True
                else:
                    print("[WARNING] Method 2: Không thấy ảnh trong UI")

            except Exception as e:
                print(f"[WARNING] Method 2 thất bại: {e}")

        # Method 3: PyAutoGUI fallback
        if not upload_success:
            try:
                print("[INFO] Method 3: Sử dụng pyautogui...")
                upload_success = upload_with_pyautogui_improved(driver, valid_paths)

                if upload_success:
                    print("[SUCCESS] Method 3: Upload thành công!")
                    return True

            except Exception as pyautogui_error:
                print(f"[WARNING] Method 3 thất bại: {pyautogui_error}")

        print("[ERROR] Tất cả methods upload đều thất bại!")
        return False

    except Exception as e:
        print(f"[ERROR] Lỗi trong upload_files_to_facebook_improved: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_upload_success(driver, expected_count):
    """Enhanced verification of upload success"""
    try:
        print(f"[INFO] Kiểm tra upload success, mong đợi {expected_count} ảnh...")

        # Multiple selectors to check for uploaded content
        upload_check_selectors = [
            ("Blob images", "//img[contains(@src, 'blob:')]"),
            ("Data images", "//img[contains(@src, 'data:')]"),
            ("Background images", "//div[contains(@style, 'background-image')]"),
            ("Uploaded divs", "//div[contains(@aria-label, 'uploaded')]"),
            ("Upload class", "//div[contains(@class, 'uploaded')]"),
            ("Media preview", "//div[contains(@class, 'media')]//img"),
            ("Preview images", "//div[contains(@class, 'preview')]//img"),
            ("Attachment images", "//div[contains(@class, 'attachment')]//img")
        ]

        total_found = 0
        for selector_name, selector in upload_check_selectors:
            try:
                elements = driver.find_elements(By.XPATH, selector)
                if elements:
                    print(f"[SUCCESS] {selector_name}: Tìm thấy {len(elements)} element(s)")
                    total_found = max(total_found, len(elements))
            except Exception as e:
                print(f"[DEBUG] {selector_name} check failed: {e}")

        if total_found >= expected_count:
            print(f"[SUCCESS] Verification passed: {total_found} >= {expected_count}")
            return True
        elif total_found > 0:
            print(f"[PARTIAL] Tìm thấy {total_found} ảnh, mong đợi {expected_count}")
            return True  # Accept partial success
        else:
            print(f"[FAILED] Không tìm thấy ảnh nào đã upload")
            return False

    except Exception as e:
        print(f"[ERROR] Verification failed: {e}")
        return False

def try_add_more_files(driver, file_path):
    """Try to add more files using various add buttons"""
    try:
        add_selectors = [
            "//div[contains(@aria-label, 'Add more')]",
            "//span[contains(text(), 'Add more')]",
            "//div[contains(text(), 'Thêm')]",
            "//span[text()='+']",
            "//div[@role='button'][contains(., '+')]",
            "//button[contains(text(), 'Add')]",
            "//div[contains(@class, 'add')][@role='button']"
        ]

        for selector in add_selectors:
            try:
                add_btn = driver.find_element(By.XPATH, selector)
                add_btn.click()
                time.sleep(2)

                # Try to use new input
                new_inputs = driver.find_elements(By.XPATH, "//input[@type='file']")
                if new_inputs:
                    new_inputs[-1].send_keys(file_path)
                    print(f"[INFO] Đã thêm file qua nút Add")
                    return True
            except:
                continue

        return False

    except Exception as e:
        print(f"[ERROR] Add more files failed: {e}")
        return False

def upload_with_pyautogui_improved(driver, image_paths):
    """Improved PyAutoGUI upload method - đi đến thư mục image cố định"""
    try:
        print("[INFO] Sử dụng pyautogui để upload (improved)...")

        # Sử dụng đường dẫn cố định đến thư mục image - FIX ENCODING
        target_folder = os.path.normpath(r"C:\Users\<USER>\Documents\Zalo Received Files\gift\gift\image")
        print(f"[INFO] Target folder: {target_folder}")

        # Debug: Print actual path
        print(f"[DEBUG] Normalized path: {repr(target_folder)}")

        # Kiểm tra thư mục có tồn tại không
        if not os.path.exists(target_folder):
            print(f"[ERROR] Thư mục không tồn tại: {target_folder}")
            # Try alternative paths
            alt_paths = [
                r"C:\Users\<USER>\Documents\Zalo Received Files\gift\gift\image",
                os.path.join(os.getcwd(), "image"),
                "image"
            ]
            for alt_path in alt_paths:
                if os.path.exists(alt_path):
                    target_folder = os.path.normpath(alt_path)
                    print(f"[INFO] Using alternative path: {target_folder}")
                    break
            else:
                return False

        # Kiểm tra có file ảnh nào trong thư mục không
        image_extensions = ('.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff', '.tif')
        image_files = [f for f in os.listdir(target_folder)
                      if f.lower().endswith(image_extensions)]

        if not image_files:
            print(f"[ERROR] Không có file ảnh nào trong thư mục: {target_folder}")
            return False

        print(f"[INFO] Tìm thấy {len(image_files)} file ảnh trong thư mục:")
        for img in image_files[:5]:  # Show first 5
            print(f"  - {img}")
        if len(image_files) > 5:
            print(f"  ... và {len(image_files) - 5} file khác")

        # Click file input to open dialog with enhanced retry
        dialog_opened = False
        for attempt in range(5):  # Increased attempts
            try:
                print(f"[INFO] Attempt {attempt + 1}: Looking for file input...")

                # Try multiple selectors for file input
                file_input_selectors = [
                    "//input[@type='file']",
                    "//input[@accept*='image']",
                    "//input[contains(@accept, 'image/*')]",
                    "//input[@multiple]"
                ]

                file_input = None
                for selector in file_input_selectors:
                    try:
                        inputs = driver.find_elements(By.XPATH, selector)
                        if inputs:
                            file_input = inputs[0]
                            print(f"[SUCCESS] Found file input with selector: {selector}")
                            break
                    except:
                        continue

                if not file_input:
                    print(f"[WARNING] No file input found in attempt {attempt + 1}")
                    time.sleep(2)
                    continue

                # Try different click methods
                click_methods = [
                    lambda: file_input.click(),
                    lambda: driver.execute_script("arguments[0].click();", file_input),
                    lambda: driver.execute_script("arguments[0].dispatchEvent(new MouseEvent('click', {bubbles: true}));", file_input)
                ]

                for i, click_method in enumerate(click_methods):
                    try:
                        print(f"[INFO] Trying click method {i + 1}...")
                        driver.execute_script("arguments[0].scrollIntoView(true);", file_input)
                        time.sleep(1)
                        click_method()
                        print(f"[SUCCESS] Click method {i + 1} succeeded!")
                        time.sleep(1)  # Wait for dialog
                        dialog_opened = True
                        break
                    except Exception as click_error:
                        print(f"[WARNING] Click method {i + 1} failed: {click_error}")
                        continue

                if dialog_opened:
                    break

            except Exception as e:
                print(f"[WARNING] Attempt {attempt + 1} failed: {e}")
                if attempt < 4:
                    time.sleep(2)

        if not dialog_opened:
            print("[ERROR] Cannot open file dialog after all attempts")
            return False

        try:
            # Navigate to target folder using address bar with encoding fix
            print(f"[INFO] Navigating to target folder: {target_folder}")

            # Type path nhanh hơn
            pyautogui.typewrite(target_folder, interval=0.01)  # Nhanh hơn từ 0.02 xuống 0.01
            pyautogui.press('enter')

            # Click vào vùng file list để đảm bảo focus đúng
            screen_width, screen_height = pyautogui.size()
            center_x = screen_width // 2
            center_y = screen_height // 2
            pyautogui.click(center_x, center_y)
            time.sleep(0.3)  # Giảm từ 0.5s xuống 0.3s

            # Select all files nhanh hơn
            pyautogui.hotkey('ctrl', 'a')
            time.sleep(0.5)  # Giảm từ 1s xuống 0.5s

            # Open files nhanh hơn
            pyautogui.press('enter')
            time.sleep(1.5)  # Giảm từ 2s xuống 1.5s

            print(f"[SUCCESS] PyAutoGUI: Attempted to upload {len(image_files)} files from {target_folder}")

            # Enhanced verification
            time.sleep(2)
            upload_success = verify_upload_success(driver, len(image_files))

            if upload_success:
                print("[SUCCESS] PyAutoGUI: Verified uploaded images!")
                return True
            else:
                print("[INFO] PyAutoGUI: Upload completed - assuming success")
                return True  # PyAutoGUI method completed successfully

        except Exception as gui_error:
            print(f"[ERROR] PyAutoGUI operations failed: {gui_error}")

            # Fallback: try typing individual file paths
            try:
                print("[INFO] Trying fallback method - typing first file path...")
                first_file = os.path.join(target_folder, image_files[0])
                print(f"[INFO] First file path: {first_file}")
                pyautogui.typewrite(first_file, interval=0.05)
                time.sleep(1)
                pyautogui.press('enter')
                time.sleep(2)
                print("[INFO] Fallback method completed")
                return True
            except Exception as fallback_error:
                print(f"[ERROR] Fallback method failed: {fallback_error}")
                return False

    except Exception as e:
        print(f"[ERROR] PyAutoGUI upload failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def upload_files_to_facebook(driver, image_paths):
    """Upload files to Facebook - improved method"""
    try:
        print(f"[INFO] Đang upload {len(image_paths)} file lên Facebook...")

        # Validate image paths first
        valid_paths = []
        for path in image_paths:
            abs_path = os.path.abspath(path)
            if os.path.exists(abs_path) and os.path.getsize(abs_path) > 0:
                valid_paths.append(abs_path)
                print(f"[VALID] {os.path.basename(abs_path)} ({os.path.getsize(abs_path)} bytes)")
            else:
                print(f"[INVALID] {abs_path} - file không tồn tại hoặc rỗng")

        if not valid_paths:
            print("[ERROR] Không có file hợp lệ để upload!")
            return False

        print(f"[INFO] Sẽ upload {len(valid_paths)} file hợp lệ...")

        # Wait a bit for the upload dialog to fully load
        time.sleep(2)

        # Find file input with multiple attempts
        file_input = None
        input_selectors = [
            "//input[@type='file']",
            "//input[@accept*='image']",
            "//input[contains(@accept, 'image/*')]"
        ]

        for selector in input_selectors:
            try:
                file_inputs = driver.find_elements(By.XPATH, selector)
                if file_inputs:
                    file_input = file_inputs[0]
                    print(f"[SUCCESS] Found file input with selector: {selector}")
                    break
            except:
                continue

        if not file_input:
            print("[ERROR] Không tìm thấy file input!")
            return False

        # Method 1: Upload all files at once
        try:
            print("[INFO] Method 1: Upload tất cả file cùng lúc...")

            # Join all file paths with newline
            all_files = "\n".join(valid_paths)
            print(f"[DEBUG] Uploading files:\n{all_files}")

            # Clear any existing value and send files
            file_input.clear()
            file_input.send_keys(all_files)

            print("[INFO] Đã gửi file paths, đang chờ xử lý...")
            time.sleep(5)  # Wait longer for processing

            # Check for uploaded content with multiple selectors
            upload_check_selectors = [
                "//img[contains(@src, 'blob:')]",
                "//img[contains(@src, 'data:')]",
                "//div[contains(@style, 'background-image')]",
                "//div[contains(@aria-label, 'uploaded')]",
                "//div[contains(@class, 'uploaded')]"
            ]

            uploaded_found = False
            for selector in upload_check_selectors:
                elements = driver.find_elements(By.XPATH, selector)
                if elements:
                    print(f"[SUCCESS] Method 1: Tìm thấy {len(elements)} ảnh đã upload với selector: {selector}")
                    uploaded_found = True
                    break

            if uploaded_found:
                return True
            else:
                print("[WARNING] Method 1: Không thấy ảnh trong UI sau khi upload")

        except Exception as e:
            print(f"[WARNING] Method 1 thất bại: {e}")

        # Method 2: Upload từng file một
        try:
            print("[INFO] Method 2: Upload từng file một...")

            # Re-find file input for method 2
            file_inputs = driver.find_elements(By.XPATH, "//input[@type='file']")
            if not file_inputs:
                print("[ERROR] Không tìm thấy file input cho Method 2")
                return False

            # Upload first file
            first_file = valid_paths[0]
            print(f"[INFO] Upload file đầu tiên: {os.path.basename(first_file)}")

            file_inputs[0].clear()
            file_inputs[0].send_keys(first_file)
            time.sleep(3)

            # For additional files, try to find "add more" functionality
            for i, file_path in enumerate(valid_paths[1:], 1):
                print(f"[INFO] Thêm file {i+1}: {os.path.basename(file_path)}")

                # Look for additional file inputs or add buttons
                time.sleep(2)
                current_inputs = driver.find_elements(By.XPATH, "//input[@type='file']")

                if len(current_inputs) > i:
                    # Use additional input if available
                    current_inputs[i].send_keys(file_path)
                    print(f"[INFO] Sử dụng input thứ {i+1}")
                else:
                    # Try to find add more button
                    add_selectors = [
                        "//div[contains(@aria-label, 'Add more')]",
                        "//span[contains(text(), 'Add more')]",
                        "//div[contains(text(), 'Thêm')]",
                        "//span[text()='+']",
                        "//div[@role='button'][contains(., '+')]"
                    ]

                    add_clicked = False
                    for add_selector in add_selectors:
                        try:
                            add_btn = driver.find_element(By.XPATH, add_selector)
                            add_btn.click()
                            time.sleep(2)

                            # Try to use new input
                            new_inputs = driver.find_elements(By.XPATH, "//input[@type='file']")
                            if len(new_inputs) > len(current_inputs):
                                new_inputs[-1].send_keys(file_path)
                                print(f"[INFO] Đã thêm file qua nút Add")
                                add_clicked = True
                                break
                        except:
                            continue

                    if not add_clicked:
                        print(f"[WARNING] Không thể thêm file thứ {i+1}, dừng ở {i} file")
                        break

            time.sleep(5)

            # Check for uploaded images
            for selector in upload_check_selectors:
                elements = driver.find_elements(By.XPATH, selector)
                if elements:
                    print(f"[SUCCESS] Method 2: Tìm thấy {len(elements)} ảnh đã upload!")
                    return True

            print("[WARNING] Method 2: Không thấy ảnh trong UI")

        except Exception as e:
            print(f"[WARNING] Method 2 thất bại: {e}")

        # Method 3: PyAutoGUI fallback
        try:
            print("[INFO] Method 3: Sử dụng pyautogui...")
            return upload_with_pyautogui(driver, valid_paths)

        except Exception as pyautogui_error:
            print(f"[WARNING] Method 3 thất bại: {pyautogui_error}")

        print("[ERROR] Tất cả methods upload đều thất bại!")
        return False

    except Exception as e:
        print(f"[ERROR] Lỗi trong upload_files_to_facebook: {e}")
        import traceback
        traceback.print_exc()
        return False

def upload_with_pyautogui(driver, image_paths):
    """Upload using pyautogui as fallback method"""
    try:
        print("[INFO] Sử dụng pyautogui để upload...")

        if not image_paths:
            print("[ERROR] Không có file để upload")
            return False

        # Get the folder containing the images
        first_file_dir = os.path.dirname(os.path.abspath(image_paths[0]))
        print(f"[DEBUG] Image folder: {first_file_dir}")

        # Click file input to open dialog
        try:
            file_input = driver.find_element(By.XPATH, "//input[@type='file']")
            driver.execute_script("arguments[0].click();", file_input)
            print("[INFO] Clicked file input, waiting for dialog...")
            time.sleep(4)  # Wait for dialog to open
        except Exception as e:
            print(f"[ERROR] Cannot click file input: {e}")
            return False

        try:
            # Navigate to folder using address bar
            print("[INFO] Navigating to image folder...")
            pyautogui.hotkey('ctrl', 'l')  # Focus address bar
            time.sleep(1)
            pyautogui.typewrite(first_file_dir, interval=0.02)
            time.sleep(1)
            pyautogui.press('enter')
            time.sleep(3)

            # Click vào vùng file list để đảm bảo focus đúng
            print("[INFO] Click vào vùng file list để focus...")
            screen_width, screen_height = pyautogui.size()
            center_x = screen_width // 2
            center_y = screen_height // 2
            pyautogui.click(center_x, center_y)
            time.sleep(0.5)

            # Select all image files
            print("[INFO] Selecting all files...")
            pyautogui.hotkey('ctrl', 'a')
            time.sleep(2)

            # Open selected files
            print("[INFO] Opening files...")
            pyautogui.press('enter')
            time.sleep(6)  # Wait for upload to process

            print(f"[SUCCESS] PyAutoGUI: Attempted to upload {len(image_paths)} files")

            # Verify upload by checking for images in UI
            time.sleep(3)
            upload_check_selectors = [
                "//img[contains(@src, 'blob:')]",
                "//img[contains(@src, 'data:')]",
                "//div[contains(@style, 'background-image')]"
            ]

            for selector in upload_check_selectors:
                elements = driver.find_elements(By.XPATH, selector)
                if elements:
                    print(f"[SUCCESS] PyAutoGUI: Verified {len(elements)} uploaded images!")
                    return True

            print("[INFO] PyAutoGUI: Upload completed - assuming success")
            return True  # PyAutoGUI method completed successfully

        except Exception as gui_error:
            print(f"[ERROR] PyAutoGUI operations failed: {gui_error}")

            # Try alternative approach - type first filename
            try:
                print("[INFO] Trying alternative approach...")
                first_file = os.path.abspath(image_paths[0])
                pyautogui.typewrite(first_file, interval=0.02)
                time.sleep(1)
                pyautogui.press('enter')
                time.sleep(3)
                print("[INFO] Alternative approach completed")
                return True
            except Exception as alt_error:
                print(f"[ERROR] Alternative approach failed: {alt_error}")
                return False

    except Exception as e:
        print(f"[ERROR] PyAutoGUI upload failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def upload_images_direct_urls(driver, image_urls):
    """Thử upload ảnh trực tiếp bằng URLs (không download)"""
    try:
        print(f"[INFO] Thử upload trực tiếp {len(image_urls)} URLs...")

        add_btn = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((
                By.XPATH,
                "//div[@aria-label='Ảnh/video' or @aria-label='Photo/video']"
            ))
        )
        add_btn.click()
        time.sleep(2)

        # Thử paste URLs trực tiếp
        drive_urls = []
        for url in image_urls:
            if "drive.google.com" in url:
                drive_urls.append(get_drive_direct_link(url))
            else:
                drive_urls.append(url)

        # Copy URLs to clipboard
        urls_text = "\n".join(drive_urls)
        pyperclip.copy(urls_text)
        time.sleep(1)

        # Paste vào Facebook
        active_element = driver.switch_to.active_element
        active_element.send_keys(Keys.CONTROL + "v")
        time.sleep(2)
        active_element.send_keys(Keys.ENTER)
        time.sleep(3)

        print("[INFO] Thử upload URLs trực tiếp hoàn thành!")
        return True

    except Exception as e:
        print(f"[ERROR] Upload URLs trực tiếp thất bại: {e}")
        return False

# ───── POST BÀI ─────────────────────────────────────────────────────────────────

def close_popup(driver):
    try:
        later = WebDriverWait(driver, 0).until(
            EC.element_to_be_clickable((By.XPATH, "//span[text()='Lúc khác' or text()='Not now']/ancestor::div[@role='button']"))
        )
        later.click()
        print("[INFO] Đã tắt popup (Lúc khác)")
    except:
        try:
            close_btn = WebDriverWait(driver, 0).until(
                EC.element_to_be_clickable((By.XPATH, "//div[@aria-label='Đóng' or @aria-label='Close']"))
            )
            close_btn.click()
            print("[INFO] Đã tắt popup (X)")
        except:
            pass

def post_to_facebook_group(driver, group_url, cookie_file, content, hashtag, title, file_ids_to_delete=None):
    if not login_with_cookies(driver, group_url, cookie_file):
        return False

    post_msg = f"{title}\n\n{content}\n\n{hashtag}"
    close_popup(driver)

    post_box = WebDriverWait(driver, 10).until(
    EC.element_to_be_clickable((
        By.XPATH,
        "//span[contains(text(), 'Bạn đang nghĩ gì?') or contains(text(), 'Bạn viết gì đi') or contains(text(), \"What's on your mind?\")]"
    ))
)
    driver.execute_script("arguments[0].click();", post_box)
    time.sleep(1)

    active_box = driver.switch_to.active_element
    active_box.send_keys(post_msg)
    time.sleep(1)

    # Upload ảnh từ thư mục local
    if file_ids_to_delete is None:
        file_ids_to_delete = []
    print("[INFO] Kiểm tra ảnh để upload...")

    # Kiểm tra thư mục local có ảnh không - FIX ENCODING
    local_image_folder = os.path.normpath(r"C:\Users\<USER>\Documents\Zalo Received Files\gift\gift\image")
    has_local_images = False

    print(f"[DEBUG] Checking local folder: {repr(local_image_folder)}")

    if os.path.exists(local_image_folder):
        image_extensions = ('.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff', '.tif')
        local_images = [f for f in os.listdir(local_image_folder)
                       if f.lower().endswith(image_extensions)]
        if local_images:
            has_local_images = True
            print(f"[INFO] Tìm thấy {len(local_images)} ảnh trong thư mục local")
    else:
        # Try alternative paths
        alt_paths = [
            r"C:\Users\<USER>\Documents\Zalo Received Files\gift\gift\image",
            os.path.join(os.getcwd(), "image"),
            "image"
        ]
        for alt_path in alt_paths:
            if os.path.exists(alt_path):
                local_image_folder = os.path.normpath(alt_path)
                print(f"[INFO] Found alternative path: {local_image_folder}")
                image_extensions = ('.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff', '.tif')
                local_images = [f for f in os.listdir(local_image_folder)
                               if f.lower().endswith(image_extensions)]
                if local_images:
                    has_local_images = True
                    print(f"[INFO] Tìm thấy {len(local_images)} ảnh trong thư mục alternative")
                break

    if has_local_images:
        # Upload từ thư mục local (ảnh đã được download sẵn từ main())
        print("[INFO] Upload ảnh từ thư mục local...")
        upload_success, _ = upload_images_from_local_folder(driver)
        if not upload_success:
            print("[ERROR] Upload ảnh từ thư mục local thất bại!")
            # Không return False, vẫn tiếp tục đăng bài chỉ có text
    else:
        print("[INFO] Không có ảnh để upload")

    # Chờ một chút để ảnh được xử lý
    time.sleep(0.5)  # Giảm từ 1s xuống 0.5s

    # Thử click Next nếu có
    try:
        next_btn = WebDriverWait(driver, 1).until(  # Giảm từ 2s xuống 1s
            EC.element_to_be_clickable((By.XPATH, "//span[text()='Tiếp' or text()='Next']/ancestor::div[@role='button']"))
        )
        next_btn.click()
        time.sleep(0.5)  # Giảm từ 1s xuống 0.5s
        print("[INFO] Clicked Next button")
    except:
        pass

    # Click Đăng
    try:
        post_btn = WebDriverWait(driver, 1).until(  # Giảm từ 2s xuống 1s
            EC.element_to_be_clickable((By.XPATH, "//span[text()='Đăng' or text()='Post']/ancestor::div[@role='button']"))
        )
        post_btn.click()
        time.sleep(0.5)  # Giảm từ 1s xuống 0.5s
        print("[INFO] Clicked Post button")

        # Chỉ log thành công, không xóa ảnh ở đây
        print("[INFO] Đăng bài thành công!")

    except Exception as e:
        print(f"[ERROR] Không tìm thấy nút Đăng: {e}")
        return False

    close_popup(driver)
    print("[INFO] Đăng bài thành công! ")
    return True

def chunk_list(lst, n):
    """Chia danh sách thành n phần gần bằng nhau"""
    avg = math.ceil(len(lst) / n)
    return [lst[i * avg: (i + 1) * avg] for i in range(n)]



def main():
    # Add system compatibility check
    print(f"[INFO] Running on {platform.system()} {platform.release()}")

    try:
        sheet_data = get_google_sheet_data()
    except Exception as e:
        print(f"[ERROR] Không thể kết nối Google Sheet: {e}")
        return

    if not sheet_data:
        print("[ERROR] Không có dữ liệu trên Google Sheet")
        return

    title = sheet_data[0].get("Title", "").strip()
    content = sheet_data[0].get("Content", "").strip()
    hashtag = sheet_data[0].get("Hashtag", "").strip()
    image_input = sheet_data[0].get("Link ảnh", "").strip()

    # Xử lý image URLs (có thể là folder hoặc danh sách file)
    image_data = process_image_urls(image_input)

    # Hiển thị thông tin ảnh
    if image_data and image_data.get('images'):
        images = image_data['images']
        print(f"[INFO] Tìm thấy {len(images)} ảnh để đăng")
        if image_data.get('type') == 'folder':
            print(f"[INFO] Từ Google Drive folder: {image_data.get('folder_url')}")
            print("[INFO] Ảnh sẽ được xóa khỏi folder sau khi đăng thành công!")

        # Download ảnh một lần duy nhất ở đây
        print("[INFO] Đang download ảnh...")
        downloaded_paths, file_ids_to_delete = download_images_from_data(image_data, save_folder="image")
        print(f"[INFO] Đã download {len(downloaded_paths)} ảnh vào folder local")
    else:
        print("[INFO] Không có ảnh để đăng")
        file_ids_to_delete = []

    accounts = [
        { "cookie": "./facebook_cookies.json" },

    ]

    group_links = [
        # 👉 Thêm đủ 100 group tại đây
    "https://www.facebook.com/groups/****************"
    ]

    random.shuffle(group_links)
    group_chunks = chunk_list(group_links, len(accounts))

    for acc, groups in zip(accounts, group_chunks):
        print(f"\n[INFO] Đăng bài với tài khoản: {acc['cookie']}, tổng số group: {len(groups)}")
        for group_url in groups:
            driver = None
            try:
                driver = get_driver()
                print(f"[INFO] Đăng vào group: {group_url}")
                success = post_to_facebook_group(driver, group_url, acc["cookie"], content, hashtag, title, file_ids_to_delete)
                if success:
                    print(f"[SUCCESS] Đăng bài thành công vào group: {group_url}")
                else:
                    print(f"[ERROR] Đăng bài thất bại vào group: {group_url}")
            except Exception as e:
                print(f"[ERROR] Lỗi khi xử lý group {group_url}: {e}")
            finally:
                if driver:
                    try:
                        driver.quit()
                    except:
                        pass
            time.sleep(30)  # Giảm từ 60s xuống 30s

    # Sau khi đăng tất cả groups, dọn dẹp ảnh
    print("\n[INFO] Hoàn thành đăng bài tất cả groups!")

    # Xóa ảnh khỏi Google Drive nếu là folder
    if file_ids_to_delete and image_data and image_data.get('type') == 'folder':
        print(f"[INFO] Đang xóa {len(file_ids_to_delete)} ảnh khỏi Google Drive...")
        delete_success = delete_files_from_drive(file_ids_to_delete)
        if delete_success:
            print("[SUCCESS] Đã xóa tất cả ảnh khỏi Google Drive!")
        else:
            print("[WARNING] Một số ảnh không thể xóa khỏi Google Drive")

    # Dọn dẹp folder ảnh local
    print("[INFO] Đang dọn dẹp folder ảnh local...")
    cleanup_success = cleanup_downloaded_images("image")
    if cleanup_success:
        print("[SUCCESS] Đã dọn dẹp folder ảnh local!")
    else:
        print("[WARNING] Một số file local không thể xóa")

if __name__ == "__main__":
    main()
